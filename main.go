package main

import (
	"os"
	"strconv"
	"time"
	"wlhy/model"
	"wlhy/thirdparty/cmb"

	"github.com/xuri/excelize/v2"
)

func main() {

	cmb.QueryRecordsByWaybillNo(cmb.GetHszy(), "", "20250708")
	os.Exit(1)

	rows, err := model.DB.Table("tms_transport_note AS a").
		Joins("JOIN tms_shipper AS b ON a.create_company_id = b.company_id").
		Select([]string{"a.id", "a.freight_paid", "a.finished_time", "b.company_name"}).
		Where("a.is_delete = ? AND a.waybill_status IN (?)", 0, []int{4, 5, 8}).
		Where("a.finished_time BETWEEN ? AND ?", "2025-01-01", "2025-08-01").
		Where("a.freight_paid <= 300").
		Where("b.shipper_type = ? AND b.is_delete = ?", 1, 0).
		Rows()
	if err != nil {
		panic(err)
	}
	defer rows.Close()

	mm := make(map[string]map[string]map[string]float64)

	for rows.Next() {
		var id string
		var freightPaid float64
		var finishedTime time.Time
		var companyName string
		if err := rows.Scan(&id, &freightPaid, &finishedTime, &companyName); err != nil {
			panic(err)
		}

		if _, ok := mm[companyName]; !ok {
			mm[companyName] = make(map[string]map[string]float64)
		}

		ft := finishedTime.Format("2006-01")
		if _, ok := mm[companyName][ft]; !ok {
			mm[companyName][ft] = make(map[string]float64)
		}
		mm[companyName][ft]["amount"] += freightPaid
		mm[companyName][ft]["count"] += 1

		if _, ok := mm[companyName]["total"]; !ok {
			mm[companyName]["total"] = make(map[string]float64)
		}
		mm[companyName]["total"]["amount"] += freightPaid
		mm[companyName]["total"]["count"] += 1
	}

	f := excelize.NewFile()

	cellMap := map[string][]string{
		"total":   []string{"B", "C"},
		"2025-01": []string{"D", "E"},
		"2025-02": []string{"F", "G"},
		"2025-03": []string{"H", "I"},
		"2025-04": []string{"J", "K"},
		"2025-05": []string{"L", "M"},
		"2025-06": []string{"N", "O"},
		"2025-07": []string{"P", "Q"},
	}

	mapSort := []string{"total", "2025-01", "2025-02", "2025-03", "2025-04", "2025-05", "2025-06", "2025-07"}

	cellIndex := 1
	for companyName, companyData := range mm {
		f.SetCellValue("Sheet1", "A"+strconv.Itoa(cellIndex), companyName)
		for _, ft := range mapSort {
			data := companyData[ft]
			f.SetCellValue("Sheet1", cellMap[ft][0]+strconv.Itoa(cellIndex), data["count"])
			f.SetCellValue("Sheet1", cellMap[ft][1]+strconv.Itoa(cellIndex), data["amount"])
		}
		cellIndex++
	}

	if err := f.SaveAs("客户运费.xlsx"); err != nil {
		panic(err)
	}
}
