package jinron

import (
	"wlhy/toolbox/logger"

	"github.com/rs/zerolog"
)

type ETC struct {
	ApiKey        string
	TaxPlayerCode string
	Logger        *zerolog.Logger
}

var ApiKey = "HprY7Bks"
var TaxPlayerCode = "91150402MACRQHHW1K"

func NewETC() *ETC {
	if ApiKey == "" {
		return nil
	}

	logOpt := logger.DefaultOptions()
	logOpt.FilePrefix = "etc"
	l, err := logger.NewLogger(logOpt)
	if err != nil {
		panic(err)
	}

	return &ETC{
		ApiKey:        ApiKey,
		TaxPlayerCode: TaxPlayerCode,
		Logger:        l.GetZerolog(),
	}
}

/* https://tms.jrerdangjia.com/api/v2.0/waybill/actualFull   实时完整运单
https://tms.jrerdangjia.com/api/v2.0/waybill/start   实时运单开始
https://tms.jrerdangjia.com/api/v2.0/waybill/end   实时运单结束
https://tms.jrerdangjia.com/api/v2.0/waybill/startHistory   历史运单开始
https://tms.jrerdangjia.com/api/v2.0/waybill/endHistory   历史运单结束
https://tms.jrerdangjia.com/api/v2.0/waybill/queryInvoice   根据运单单号查询发票
https://tms.jrerdangjia.com/api/v2.0/waybill/cancelWaybill   根据运单单号取消运单
https://tms.jrerdangjia.com/api/v2.0/waybill/truckRegister 车辆备案 */
