package logger

import (
	"context"
	"fmt"
	"os"
	"path/filepath"
	"strings"
	"sync"
	"sync/atomic"
	"time"

	"github.com/rs/zerolog"
	"github.com/rs/zerolog/pkgerrors"
)

// 全局默认日志实例，使用原子指针确保线程安全
var (
	defaultLogger atomic.Pointer[Logger]
	initOnce      sync.Once
)

// Options 日志配置选项
type Options struct {
	// 基础配置
	FilePrefix string        // 日志文件前缀
	LogDir     string        // 日志目录
	Level      zerolog.Level // 日志级别
	TimeFormat string        // 时间格式

	// 输出配置
	FileMode os.FileMode // 文件权限
	DirMode  os.FileMode // 目录权限

	// 性能配置
	BufferSize   int           // 缓冲区大小
	FlushTimeout time.Duration // 刷新超时时间
	MaxFileSize  int64         // 最大文件大小（字节）

	// 高级配置
	CallerSkipFrameCount int  // 调用栈跳过帧数
	AddSource            bool // 是否添加源码位置
}

// DefaultOptions 返回默认配置
func DefaultOptions() *Options {
	return &Options{
		FilePrefix:           "",
		LogDir:               "logs",
		Level:                zerolog.InfoLevel,
		TimeFormat:           "2006-01-02 15:04:05",
		FileMode:             0644,
		DirMode:              0755,
		BufferSize:           4096,
		FlushTimeout:         time.Second,
		MaxFileSize:          100 * 1024 * 1024, // 100MB
		CallerSkipFrameCount: 3,
		AddSource:            true,
	}
}

// Logger 高性能日志实例
type Logger struct {
	zlog   *zerolog.Logger
	writer *RotatingFileWriter
	opts   *Options
	ctx    context.Context
	cancel context.CancelFunc
}

// RotatingFileWriter 高性能日志文件轮转写入器
type RotatingFileWriter struct {
	opts        *Options
	file        *os.File
	currentDate string
	fileSize    int64
	buffer      []byte
	bufferPos   int
	mu          sync.RWMutex
	flushTimer  *time.Timer
	closed      int32 // 使用原子操作
}

// NewRotatingFileWriter 创建新的轮转文件写入器
func NewRotatingFileWriter(opts *Options) (*RotatingFileWriter, error) {
	w := &RotatingFileWriter{
		opts:   opts,
		buffer: make([]byte, opts.BufferSize),
	}

	// 初始化文件
	if err := w.rotateIfNeeded(); err != nil {
		return nil, fmt.Errorf("初始化日志文件失败: %w", err)
	}

	// 启动定时刷新
	w.startFlushTimer()
	return w, nil
}

// getLogFileName 生成日志文件名
func (w *RotatingFileWriter) getLogFileName(date string) string {
	var filename string
	if w.opts.FilePrefix != "" {
		filename = fmt.Sprintf("%s_%s.log", w.opts.FilePrefix, date)
	} else {
		filename = fmt.Sprintf("%s.log", date)
	}
	return filepath.Join(w.opts.LogDir, filename)
}

// rotateIfNeeded 检查并执行日志轮转
func (w *RotatingFileWriter) rotateIfNeeded() error {
	currentDate := time.Now().Format("20060102")

	// 检查是否需要按日期轮转
	needRotateByDate := w.currentDate != currentDate
	// 检查是否需要按大小轮转
	needRotateBySize := w.opts.MaxFileSize > 0 && w.fileSize >= w.opts.MaxFileSize

	if !needRotateByDate && !needRotateBySize {
		return nil
	}

	// 刷新缓冲区
	if err := w.flushBuffer(); err != nil {
		return fmt.Errorf("刷新缓冲区失败: %w", err)
	}

	// 关闭旧文件
	if w.file != nil {
		if err := w.file.Close(); err != nil {
			fmt.Fprintf(os.Stderr, "关闭旧日志文件失败: %v\n", err)
		}
	}

	// 创建日志目录
	if err := os.MkdirAll(w.opts.LogDir, w.opts.DirMode); err != nil {
		return fmt.Errorf("创建日志目录失败: %w", err)
	}

	// 生成新文件名
	filename := w.getLogFileName(currentDate)
	if needRotateBySize && !needRotateByDate {
		// 按大小轮转时添加时间戳后缀
		timestamp := time.Now().Format("150405")
		ext := filepath.Ext(filename)
		base := strings.TrimSuffix(filename, ext)
		filename = fmt.Sprintf("%s_%s%s", base, timestamp, ext)
	}

	// 打开新文件
	file, err := os.OpenFile(filename, os.O_WRONLY|os.O_CREATE|os.O_APPEND, w.opts.FileMode)
	if err != nil {
		return fmt.Errorf("打开日志文件失败: %w", err)
	}

	w.file = file
	w.currentDate = currentDate
	w.fileSize = 0
	return nil
}

// Write 实现 io.Writer 接口，支持高性能缓冲写入
func (w *RotatingFileWriter) Write(p []byte) (n int, err error) {
	if atomic.LoadInt32(&w.closed) == 1 {
		return 0, fmt.Errorf("写入器已关闭")
	}

	w.mu.Lock()
	defer w.mu.Unlock()

	// 检查是否需要轮转
	if err := w.rotateIfNeeded(); err != nil {
		return 0, fmt.Errorf("日志轮转失败: %w", err)
	}

	totalWritten := 0
	remaining := p

	for len(remaining) > 0 {
		// 计算可写入缓冲区的字节数
		available := len(w.buffer) - w.bufferPos
		if available == 0 {
			// 缓冲区已满，刷新到文件
			if err := w.flushBuffer(); err != nil {
				return totalWritten, err
			}
			available = len(w.buffer)
		}

		// 写入缓冲区
		writeSize := len(remaining)
		if writeSize > available {
			writeSize = available
		}

		copy(w.buffer[w.bufferPos:], remaining[:writeSize])
		w.bufferPos += writeSize
		totalWritten += writeSize
		remaining = remaining[writeSize:]
	}

	return totalWritten, nil
}

// flushBuffer 刷新缓冲区到文件
func (w *RotatingFileWriter) flushBuffer() error {
	if w.bufferPos == 0 || w.file == nil {
		return nil
	}

	n, err := w.file.Write(w.buffer[:w.bufferPos])
	if err != nil {
		return fmt.Errorf("写入文件失败: %w", err)
	}

	w.fileSize += int64(n)
	w.bufferPos = 0
	return nil
}

// startFlushTimer 启动定时刷新
func (w *RotatingFileWriter) startFlushTimer() {
	if w.opts.FlushTimeout <= 0 {
		return
	}

	w.flushTimer = time.AfterFunc(w.opts.FlushTimeout, func() {
		w.mu.Lock()
		defer w.mu.Unlock()

		if atomic.LoadInt32(&w.closed) == 0 {
			w.flushBuffer()
			w.startFlushTimer() // 重新启动定时器
		}
	})
}

// Close 关闭写入器
func (w *RotatingFileWriter) Close() error {
	if !atomic.CompareAndSwapInt32(&w.closed, 0, 1) {
		return nil // 已经关闭
	}

	w.mu.Lock()
	defer w.mu.Unlock()

	// 停止定时器
	if w.flushTimer != nil {
		w.flushTimer.Stop()
	}

	// 刷新缓冲区
	if err := w.flushBuffer(); err != nil {
		fmt.Fprintf(os.Stderr, "关闭时刷新缓冲区失败: %v\n", err)
	}

	// 关闭文件
	if w.file != nil {
		err := w.file.Close()
		w.file = nil
		return err
	}

	return nil
}

// NewLogger 创建新的高性能日志实例
func NewLogger(opts *Options) (*Logger, error) {
	if opts == nil {
		opts = DefaultOptions()
	}

	// 配置 zerolog 全局设置
	zerolog.TimestampFieldName = "t"
	zerolog.LevelFieldName = "l"
	zerolog.MessageFieldName = "m"
	zerolog.TimeFieldFormat = opts.TimeFormat
	zerolog.ErrorStackMarshaler = pkgerrors.MarshalStack

	// 创建上下文
	ctx, cancel := context.WithCancel(context.Background())

	logger := &Logger{
		opts:   opts,
		ctx:    ctx,
		cancel: cancel,
	}

	// 创建文件写入器
	fileWriter, err := NewRotatingFileWriter(opts)
	if err != nil {
		cancel()
		return nil, fmt.Errorf("创建文件写入器失败: %w", err)
	}
	logger.writer = fileWriter

	// 创建 zerolog 实例，直接使用文件写入器
	zeroLogger := zerolog.New(fileWriter)

	// 配置日志级别和字段
	zeroLogger = zeroLogger.Level(opts.Level)

	if opts.AddSource {
		zeroLogger = zeroLogger.With().Timestamp().CallerWithSkipFrameCount(opts.CallerSkipFrameCount).Logger()
	} else {
		zeroLogger = zeroLogger.With().Timestamp().Logger()
	}

	logger.zlog = &zeroLogger
	return logger, nil
}

// Close 关闭日志实例
func (l *Logger) Close() error {
	l.cancel()
	if l.writer != nil {
		return l.writer.Close()
	}
	return nil
}

// GetZerolog 获取底层的 zerolog.Logger 实例，支持链式调用
// 使用示例: logger.GetZerolog().Info().Interface("key", value).Msg("message")
func (l *Logger) GetZerolog() *zerolog.Logger {
	return l.zlog
}

// formatArgs 格式化参数为字符串
func (l *Logger) formatArgs(args ...any) string {
	if len(args) == 0 {
		return ""
	}

	parts := make([]string, 0, len(args))
	for _, arg := range args {
		parts = append(parts, fmt.Sprintf("%+v", arg))
	}
	return strings.Join(parts, " ")
}

// Debug 记录调试级别日志
func (l *Logger) Debug(args ...any) {
	l.zlog.Debug().Msg(l.formatArgs(args...))
}

// Info 记录信息级别日志
func (l *Logger) Info(args ...any) {
	l.zlog.Info().Msg(l.formatArgs(args...))
}

// Warn 记录警告级别日志
func (l *Logger) Warn(args ...any) {
	l.zlog.Warn().Msg(l.formatArgs(args...))
}

// Error 记录错误级别日志
func (l *Logger) Error(args ...any) {
	l.zlog.Error().Msg(l.formatArgs(args...))
}

// Fatal 记录致命错误级别日志并退出程序
func (l *Logger) Fatal(args ...any) {
	l.zlog.Fatal().Msg(l.formatArgs(args...))
}

// Flush 强制刷新缓冲区
func (l *Logger) Flush() error {
	if l.writer != nil {
		l.writer.mu.Lock()
		defer l.writer.mu.Unlock()
		return l.writer.flushBuffer()
	}
	return nil
}

// GetDefaultLogger 获取默认日志实例（延迟初始化）
func GetDefaultLogger() *Logger {
	if logger := defaultLogger.Load(); logger != nil {
		return logger
	}

	initOnce.Do(func() {
		logger, err := NewLogger(DefaultOptions())
		if err != nil {
			panic(fmt.Sprintf("初始化默认日志实例失败: %v", err))
		}
		defaultLogger.Store(logger)
	})

	return defaultLogger.Load()
}

// 全局便捷函数，使用默认日志实例

// Debug 记录调试级别日志
func Debug(args ...any) {
	GetDefaultLogger().Debug(args...)
}

// Info 记录信息级别日志
func Info(args ...any) {
	GetDefaultLogger().Info(args...)
}

// Warn 记录警告级别日志
func Warn(args ...any) {
	GetDefaultLogger().Warn(args...)
}

// Error 记录错误级别日志
func Error(args ...any) {
	GetDefaultLogger().Error(args...)
}

// Fatal 记录致命错误级别日志并退出程序
func Fatal(args ...any) {
	GetDefaultLogger().Fatal(args...)
}

// Close 关闭默认日志实例
func Close() error {
	if logger := defaultLogger.Load(); logger != nil {
		return logger.Close()
	}
	return nil
}

// Flush 强制刷新默认日志实例的缓冲区
func Flush() error {
	if logger := defaultLogger.Load(); logger != nil {
		return logger.Flush()
	}
	return nil
}

// SetOptions 设置新的配置并重新初始化默认日志实例
func SetOptions(opts *Options) error {
	// 关闭旧的日志实例
	if oldLogger := defaultLogger.Load(); oldLogger != nil {
		oldLogger.Close()
	}

	// 创建新的日志实例
	newLogger, err := NewLogger(opts)
	if err != nil {
		return fmt.Errorf("设置新配置失败: %w", err)
	}

	// 原子性地更新默认日志实例
	defaultLogger.Store(newLogger)

	// 重置初始化标志，允许重新初始化
	initOnce = sync.Once{}

	return nil
}

// GetZerolog 获取默认日志实例的底层 zerolog.Logger，支持链式调用
// 使用示例: logger.GetZerolog().Info().Interface("key", value).Msg("message")
func GetZerolog() *zerolog.Logger {
	return GetDefaultLogger().GetZerolog()
}
